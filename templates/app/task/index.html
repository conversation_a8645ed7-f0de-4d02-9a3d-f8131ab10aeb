{% extends "app/base_site.html" %}

{% block title %} 任务管理 {% endblock title %}

{% block stylesheets %}
  {{ block.super }}

    <style>

.image_container{
    width: 100px;
    height: 100px;
    overflow: hidden;
    position: relative;
    border-radius: 6px;
}
.image_container img{
    width: 100%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}
.face_container{
    width: 60px;
    height: 60px;
    overflow: hidden;
    border: 1px solid #6d8494;
    position: relative;
    border-radius: 6px;
}
.face_container img{
    width: 100%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}
    </style>
{% endblock stylesheets %}

{% block content %}

  <div class="right_col" role="main">
    <div class="">
      <div class="row">
          <div class="col-md-12 col-sm-12 col-xs-12">
            <div class="x_panel">
              <div class="x_title">
               <h2>{% if is_annotator %}标注任务{% else %}任务管理{% endif %}
                    <span id="top_loading" ><img class="top_loading_img" src="/static/images/load.gif" alt="loading">加载中</span>
                    <span id="top_msg">{{top_msg}}</span>

                 {% if not is_annotator %}
                 <button onclick="f_sync()" class="btn btn-default btn-sm"> 更新标注进度</button>
                 {% endif %}

                        </h2>

                <div class="clearfix"></div>
              </div>
              <div class="x_content">
                <!--<p><code>说明</code> 系统支持的行为算法</p>-->
                <div class="table-responsive">
                  <table class="table table-bordered">
                    <thead>
                      <tr class="headings">
                        <th class="column-title">ID</th>
                        <th class="column-title">创建用户</th>
                        <th class="column-title">名称</th>
                        <th class="column-title">任务类型</th>
                        <th class="column-title">标注进度</th>
                        <th class="column-title">更新时间</th>
                          <th class="column-title no-link last"><span class="nobr">操作</span></th>
                      </tr>
                    </thead>

                    <tbody>
                    {%  for d  in data %}
                      <tr class="even pointer">
                        <td>{{ d.id }}<!--{{forloop.counter}}--></td>
                        <td>{{ d.username }}</td>
                        <td>{{ d.name }}</td>
                        <td>{% if d.task_type == 1 %} 图像任务 {% endif %}</td>
                          <td>{{ d.sample_annotation_count }} / {{ d.sample_count }}</td>
                          <td>{{ d.last_update_time }}</td>
                          <td>
                              <div class="btn-group">
                              {% if not is_annotator %}
                              <button onclick="f_edit('{{ d.code }}')" class="btn btn-sm btn-default" type="button"  data-placement="top" data-toggle="tooltip" data-original-title="编辑任务"><i class="fa fa-edit"></i></button>
                              {% endif %}
                              <button onclick="f_sample('{{ d.code }}')" class="btn btn-sm btn-default" type="button" data-placement="top" data-toggle="tooltip" data-original-title="开始标注"><i class="fa fa-tags"></i></button>
                              {% if not is_annotator %}
                              <button onclick="f_train('{{ d.code }}')" class="btn btn-sm btn-default" type="button" data-placement="top" data-toggle="tooltip" data-original-title="开始训练"><i class="fa fa-train"></i></button>
                              <button onclick="f_show_export_options('{{ d.code }}')" class="btn btn-sm btn-default" type="button" data-placement="top" data-toggle="tooltip" data-original-title="导出数据"><i class="fa fa-download"></i></button>
                              <button onclick="f_model_predict('{{ d.code }}')" class="btn btn-sm btn-warning" type="button" data-placement="top" data-toggle="tooltip" data-original-title="模型预标注"><i class="fa fa-magic"></i></button>
                              <button onclick="f_del('{{ d.code }}')" class="btn btn-sm btn-default" type="button" data-placement="top" data-toggle="tooltip" data-original-title="删除任务"><i class="fa fa-remove"></i></button>
                              
                              {% endif %}
                            </div>
                        </td>
                      </tr>
                    {% endfor %}

                    </tbody>
                  </table>
                </div>
              </div>

            </div>
          </div>
      </div>

      <div class="row">
          <div class="col-md-12 col-sm-12 col-xs-12">
            <ul class="pagination">
                <li>
                    <span style="margin-right:10px;color:#000;">共<span>{{ pageData.page_num}}</span>页 / <span>{{pageData.count}}</span >条</span>
                </li>

                {%  for d  in pageData.pageLabels%}
                  {% if d.cur == 1 %}
                    <li class="paginate_button active"><a href="#"  >{{ d.name }}</a></li>
                  {% else %}
                    <li class="paginate_button "><a href="/task/index?p={{d.page}}&ps={{pageData.page_size}}" >{{ d.name }}</a></li>
                  {% endif %}
                {% endfor %}

              <!--
              <li class="paginate_button previous" ><a href="#" data-dt-idx="0" >上一页</a></li>
              <li class="paginate_button active"><a href="#" data-dt-idx="1" >1</a></li>

              <li class="paginate_button "><a href="#" data-dt-idx="2">2</a></li>
              <li class="paginate_button "><a href="#" data-dt-idx="3">3</a></li>
              <li class="paginate_button "><a href="#" data-dt-idx="4">4</a></li>
              <li class="paginate_button "><a href="#" data-dt-idx="5" >5</a></li>
              <li class="paginate_button "><a href="#" data-dt-idx="6" >6</a></li>
              <li class="paginate_button next"><a href="#" data-dt-idx="7" >下一页</a></li>
              -->
            </ul>

          </div>
       </div>


    </div>
  </div>

<!-- 模型预标注模态框 -->
<div class="modal fade" id="modelPredictModal" tabindex="-1" role="dialog" aria-labelledby="modelPredictModalLabel">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
        <h4 class="modal-title" id="modelPredictModalLabel">模型预标注</h4>
      </div>
      <div class="modal-body">
        <form id="modelPredictForm">
          <div class="form-group">
            <label for="modelPath">YOLO模型路径:</label>
            <input type="text" class="form-control" id="modelPath" placeholder="请输入YOLO模型文件路径 (例如: /path/to/model.pt)" required>
            <small class="form-text text-muted">支持 .pt 格式的YOLO模型文件</small>
          </div>
          <div class="alert alert-warning" role="alert">
            <strong>注意:</strong> 预标注会删除该任务中所有已存在的标注信息，请确认后再继续操作！
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
        <button type="button" class="btn btn-warning" id="confirmModelPredict">确认预标注</button>
      </div>
    </div>
  </div>
</div>

<!-- 导出选项模态对话框 -->
<div class="modal fade" id="exportOptionsModal" tabindex="-1" role="dialog" aria-labelledby="exportOptionsModalLabel">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
        <h4 class="modal-title" id="exportOptionsModalLabel">导出选项</h4>
      </div>
      <div class="modal-body">
        <form id="exportOptionsForm">
          <div class="form-group">
            <label>数据范围：</label>
            <div class="radio">
              <label>
                <input type="radio" name="data_scope" value="all" checked>
                导出全量数据（所有样本）
              </label>
            </div>
            <div class="radio">
              <label>
                <input type="radio" name="data_scope" value="annotated">
                仅导出已标注数据
              </label>
            </div>
            <div class="radio">
              <label>
                <input type="radio" name="data_scope" value="defect">
                仅导出有缺陷的数据（包含缺陷标签的样本）
              </label>
            </div>
          </div>

          <div class="form-group">
            <label>导出内容：</label>
            <div class="checkbox">
              <label>
                <input type="checkbox" name="include_images" checked>
                包含图像文件
              </label>
            </div>
            <div class="checkbox">
              <label>
                <input type="checkbox" name="include_annotations" checked>
                包含标注文件（未标注样本将生成空txt文件）
              </label>
            </div>
          </div>

          <div class="form-group">
            <label>导出格式：</label>
            <div class="radio">
              <label>
                <input type="radio" name="export_format" value="simple" checked>
                简单格式（图像和标注文件在同一目录）
              </label>
            </div>
            <div class="radio">
              <label>
                <input type="radio" name="export_format" value="training">
                训练格式（按训练集和验证集分目录）
              </label>
            </div>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
        <button type="button" class="btn btn-primary" onclick="f_confirm_export()">确认导出</button>
      </div>
    </div>
  </div>
</div>

{% endblock content %}

{% block javascripts %}
  {{ block.super }}
<script>
    let ele_top_loading = $("#top_loading");
    let ele_top_msg= $("#top_msg");
    function f_sample(task_code) {
        let url = "/sample/index?task_code="+task_code;
        window.open(url);
    }

     function f_train(task_code) {
        window.location.href = "/train/add?task_code="+task_code;

     }
    function f_sync() {
        ele_top_loading.show();
        $.ajax({
               url: '/task/sync',
               type: "get",
               async: true,
               data: {},
               dataType: "json",
               timeout: 0,
               error: function () {
                   ele_top_loading.hide();
                   myAlert("网络异常，请确定网络正常！","error");
               },
               success: function (res) {
                   ele_top_loading.hide();
                   if(1000 === res.code){
                        window.location.reload();
                   }else{
                        myAlert(res.msg,"error");
                   }
               }
            });
    }
    function f_docs() {
        let url= "{{ settings.docs.facedbAdd }}";
        window.open(url);
    }
    function f_edit(code) {
        let url = "/task/edit?code="+code;
        window.location.href = url;
    }
    // 全局变量存储当前要导出的任务代码
    let currentExportTaskCode = '';

    function f_show_export_options(code) {
        console.log('f_show_export_options called with code:', code);
        currentExportTaskCode = code;
        console.log('About to show modal...');
        $('#exportOptionsModal').modal('show');
        console.log('Modal show command executed');
    }

    function f_confirm_export() {
        // 获取表单数据
        const formData = new FormData();
        formData.append('code', currentExportTaskCode);

        // 获取数据范围选项
        const dataScope = $('input[name="data_scope"]:checked').val();
        formData.append('data_scope', dataScope);

        // 获取导出内容选项
        const includeImages = $('input[name="include_images"]').is(':checked');
        const includeAnnotations = $('input[name="include_annotations"]').is(':checked');
        formData.append('include_images', includeImages);
        formData.append('include_annotations', includeAnnotations);

        // 获取导出格式选项
        const exportFormat = $('input[name="export_format"]:checked').val();
        formData.append('export_format', exportFormat);

        // 关闭模态对话框
        $('#exportOptionsModal').modal('hide');

        // 执行导出
        f_export_with_options(formData);
    }

    function f_export_with_options(formData) {
        ele_top_loading.show();
        $.ajax({
               url: '/task/export',
               type: "post",
               async: true,
               data: Object.fromEntries(formData),
               dataType: "json",
               timeout: 60000, // 增加超时时间到60秒
               error: function () {
                   ele_top_loading.hide();
                   myAlert("网络异常，请确定网络正常！","error");
               },
               success: function (res) {
                   ele_top_loading.hide();
                   if(1000 === res.code){
                        // 创建下载链接
                        if(res.data && res.data.download_url) {
                            let link = document.createElement('a');
                            link.href = res.data.download_url;
                            link.download = res.data.filename || 'export_data.zip';
                            document.body.appendChild(link);
                            link.click();
                            document.body.removeChild(link);
                            myAlert("导出成功！","success");
                        } else {
                            myAlert("导出成功，但未返回下载链接！","warning");
                        }
                   }else{
                        myAlert(res.msg || "导出失败","error");
                   }
               }
            });
    }

    // 保留原有的f_export函数以兼容其他地方的调用
    function f_export(code) {
        f_show_export_options(code);
    }
    function f_del(code){
        // 添加确认提示框
        if (!confirm('确定要删除这个任务吗？删除后无法恢复！')) {
            return;
        }

        ele_top_loading.show();
        $.ajax({
               url: '/task/postDel',
               type: "post",
               async: true,
               data: {"code":code},
               dataType: "json",
               timeout: 0,
               error: function () {
                   ele_top_loading.hide();
                   myAlert("网络异常，请确定网络正常！","error");
               },
               success: function (res) {
                   ele_top_loading.hide();
                   if(1000 === res.code){
                        window.location.reload();
                   }else{
                        myAlert(res.msg,"error");
                   }
               }
            });

    }
    // 模型预标注相关函数
    let currentTaskCode = ''; // 当前选中的任务代码

    function f_model_predict(code) {
        currentTaskCode = code;
        $('#modelPath').val(''); // 清空输入框
        $('#modelPredictModal').modal('show');
    }

    function startModelPredict(taskCode, modelPath) {
        ele_top_loading.show();
        $.ajax({
            url: '/task/model_predict',
            type: "post",
            async: true,
            data: {
                "code": taskCode,
                "model_path": modelPath
            },
            dataType: "json",
            timeout: 180000, // 3分钟超时
            error: function () {
                ele_top_loading.hide();
                myAlert("网络异常，请确定网络正常！", "error");
            },
            success: function (res) {
                ele_top_loading.hide();
                if (1000 === res.code) {
                    myAlert(res.msg || "预标注成功！", "success");
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    myAlert(res.msg || "预标注失败", "error");
                }
            }
        });
    }

    $(document).ready(function () {
        // 绑定确认预标注按钮事件
        $('#confirmModelPredict').click(function() {
            const modelPath = $('#modelPath').val().trim();
            if (!modelPath) {
                myAlert("请输入模型路径", "warning");
                return;
            }

            // 关闭模态框
            $('#modelPredictModal').modal('hide');

            // 二次确认
            if (confirm('确定要使用模型进行预标注吗？这将删除该任务中所有已存在的标注信息！')) {
                startModelPredict(currentTaskCode, modelPath);
            }
        });
    });
</script>

{% endblock javascripts %}

